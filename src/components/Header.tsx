"use client";

import Link from "next/link";
import { But<PERSON> } from "./ui/button";
import { usePathname } from "next/navigation";
import { locales, localeNames } from "@/i18n/routing";
import { Menu, Globe, Search } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { signIn, signOut, useSession } from "next-auth/react";
import Image from "next/image";
import { useState, useEffect, useRef, useCallback } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ThemeToggle } from "@/components/ThemeToggle";
import { Input } from "./ui/input";
import { Popover, PopoverContent, PopoverAnchor } from "@/components/ui/popover";
import { Command, CommandItem, CommandList } from "@/components/ui/command";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import debounce from 'lodash/debounce';
import { useTranslations } from 'next-intl';

interface HeaderProps {
  header: {
    logo: string;
    nav: {
      features: string;
      pricing: string;
      examples: string;
      docs: string;
    };
    cta: {
      login: string;
      signup: string;
    };
    userMenu: {
      myOrders: string;
      signOut: string;
      profile: string;
    };
  };
}

// 定义频道建议的结构
interface ChannelSuggestion {
  channelId: string;
  name: string;
  title: string;
  thumbnailUrl?: string | null;
  customUrl?: string | null;
  subscriberCount?: number | null;
  videoCount?: number | null;
}

// 格式化订阅人数的辅助函数
function formatSubscribers(count: number | null | undefined): string | null {
  if (count === null || count === undefined || count < 0) {
    return null;
  }
  if (count >= 1000000) {
    const millions = (count / 1000000);
    return (millions % 1 === 0 ? millions.toFixed(0) : millions.toFixed(1)) + 'M';
  }
  if (count >= 1000) {
    const thousands = (count / 1000);
    return (thousands % 1 === 0 ? thousands.toFixed(0) : thousands.toFixed(1)) + 'K';
  }
  return count.toString();
}

export default function Header({ header }: HeaderProps) {
  const t = useTranslations('header.search');
  const { data: session } = useSession();
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const userDropdownRef = useRef<HTMLDivElement>(null);
  
  // 添加搜索相关状态
  const [searchQuery, setSearchQuery] = useState("");
  const [suggestions, setSuggestions] = useState<ChannelSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showTip, setShowTip] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchAnchorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target as Node)) {
        setIsUserDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const switchLocale = (locale: string) => {
    const newPathname = pathname.replace(`/${currentLocale}`, `/${locale}`);
    window.location.href = newPathname;
  };

  // 定义防抖搜索功能
  const debouncedFetch = useCallback(
    debounce(async (query: string) => {
      const trimmedQuery = query.trim();
      if (trimmedQuery.length < 3) {
        setSuggestions([]);
        setIsLoading(false);
        setIsDropdownOpen(false);
        setShowTip(true);
        return [];
      }
      setIsLoading(true);
      try {
        const response = await fetch(`/api/channels/search?query=${encodeURIComponent(trimmedQuery)}`);
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        const data: ChannelSuggestion[] = await response.json();
        setSuggestions(data);
        setIsDropdownOpen(true);
        return data;
      } catch (error) {
        console.error(t('searchError'), error);
        setSuggestions([]);
        setIsDropdownOpen(true);
        return [];
      } finally {
        setIsLoading(false);
      }
    }, 300),
    [t]
  );

  // 监听搜索查询变化
  useEffect(() => {
    if (searchQuery) {
      debouncedFetch(searchQuery);
    } else {
      setSuggestions([]);
      setIsLoading(false);
      setIsDropdownOpen(false);
      debouncedFetch.cancel();
    }
    return () => {
      debouncedFetch.cancel();
    };
  }, [searchQuery, debouncedFetch]);

  // 选择建议项的处理函数
  const handleSelectSuggestion = (suggestion: ChannelSuggestion) => {
    setIsDropdownOpen(false);
    window.location.href = `/${currentLocale}/channel/${encodeURIComponent(suggestion.channelId)}`;
  };

  return (
    <div className="mx-auto px-4 sm:px-6 lg:px-8">
      <nav className="relative flex h-16 items-center justify-between">
        {/* Left: Logo */}
        <div className="flex-none">
          <Link href="/" className="flex items-center">
            <span className="text-xl font-bold">{header.logo}</span>
          </Link>
        </div>

        {/* Center: Navigation Links - Desktop Only */}
        <div className="hidden md:flex items-center justify-center flex-grow">
          <div className="flex space-x-8">
            {Object.entries(header.nav).map(([key, value]) => (
              <Link
                key={key}
                href={`/${pathname.split('/')[1]}#${key}`}
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                {value}
              </Link>
            ))}
          </div>
        </div>

        {/* Right: Search, Language & CTA Buttons */}
        <div className="hidden md:flex items-center space-x-4">
          {/* 搜索框组件 - 移到右侧 */}
          <div className="relative w-60" ref={searchAnchorRef}>
            <Popover open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
              <PopoverAnchor asChild>
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    ref={searchInputRef}
                    type="text"
                    placeholder={t('placeholder')}
                    className="pl-8 py-1 text-sm rounded-md w-full"
                    value={searchQuery}
                    onChange={(e) => {
                      const value = e.target.value;
                      setSearchQuery(value);
                      setIsDropdownOpen(true);
                      
                      if (value.trim().length >= 3) {
                        setShowTip(false);
                        debouncedFetch(value);
                      } else {
                        setShowTip(true);
                      }
                    }}
                    onFocus={() => {
                      if (searchQuery && searchQuery.trim().length >= 3) {
                        setIsDropdownOpen(true);
                        setShowTip(false);
                      } else {
                        setShowTip(true);
                      }
                    }}
                    autoComplete="off"
                  />
                </div>
              </PopoverAnchor>

              <PopoverContent 
                className="w-[var(--radix-popover-trigger-width)] p-0" 
                align="start"
                sideOffset={5}
                onOpenAutoFocus={(e: Event) => e.preventDefault()}
              >
                <Command className="rounded-lg border shadow-md">
                  <div className="flex flex-col">
                    <div className="px-3 py-2 text-xs text-muted-foreground">
                      {t('selectChannel')}
                    </div>
                    <CommandList>
                      {showTip ? (
                        <div className="px-3 py-4 text-center">
                          <p className="text-xs text-muted-foreground">
                            {t('minCharacters')}
                          </p>
                        </div>
                      ) : isLoading ? (
                        <div className="px-3 py-4 text-center">
                          <p className="text-xs text-muted-foreground">
                            {t('searching')}
                          </p>
                        </div>
                      ) : suggestions.length === 0 && searchQuery.trim().length >= 3 ? (
                        <div className="px-3 py-4 text-center">
                          <p className="text-xs text-muted-foreground">
                            {t('noResults')}
                          </p>
                        </div>
                      ) : (
                        <>
                          {suggestions.map((suggestion) => (
                            <CommandItem
                              key={suggestion.channelId}
                              onSelect={() => handleSelectSuggestion(suggestion)}
                              className="flex items-center gap-2 p-2 rounded-lg hover:bg-muted transition-colors"
                            >
                              <Avatar className="h-6 w-6 flex-shrink-0">
                                <AvatarImage src={suggestion.thumbnailUrl || "/images/placeholder.jpg"} />
                                <AvatarFallback>{suggestion.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <div className="flex-1 min-w-0">
                                <p className="font-medium text-xs truncate">{suggestion.title}</p>
                                {suggestion.customUrl && (
                                  <p className="text-xs text-muted-foreground truncate">
                                    {suggestion.customUrl}
                                  </p>
                                )}
                                <p className="text-xs text-muted-foreground truncate">
                                  {formatSubscribers(suggestion.subscriberCount)} {t('subscribers')}
                                </p>
                              </div>
                            </CommandItem>
                          ))}
                        </>
                      )}
                    </CommandList>
                  </div>
                </Command>
              </PopoverContent>
            </Popover>
          </div>

          <ThemeToggle />
          <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900">
              <Globe className="h-4 w-4" />
              <span>{localeNames[currentLocale]}</span>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {locales.map((locale) => (
                <DropdownMenuItem
                  key={locale}
                  onClick={() => switchLocale(locale)}
                  className="cursor-pointer"
                >
                  {localeNames[locale]}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <div className="flex items-center space-x-3">
            {session ? (
              <div className="relative" ref={userDropdownRef}>
                <div>
                  <button
                    type="button"
                    className="flex items-center rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
                  >
                    <span className="sr-only">{t('openMenu')}</span>
                    {session.user?.image ? (
                      <Image
                        className="h-8 w-8 rounded-full"
                        src={session.user.image}
                        alt={session.user.name || ''}
                        width={32}
                        height={32}
                        unoptimized
                        priority
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-600">
                          {session.user?.name?.charAt(0) || '?'}
                        </span>
                      </div>
                    )}
                  </button>
                </div>

                {isUserDropdownOpen && (
                  <div className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5">
                    <div className="px-4 py-2 text-sm text-gray-700">
                      <div className="font-medium">{session.user?.name}</div>
                    </div>
                    <div className="px-4 py-2 text-sm text-gray-700">
                      <div className="text-gray-500">{session.user?.email}</div>
                    </div>
                    <div className="border-t border-gray-100" />
                    <button
                      type="button"
                      onClick={() => signOut({ callbackUrl: `/${currentLocale}` })}
                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-pink-50"
                    >
                      {header.userMenu.signOut}
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center">
                <Button 
                  onClick={() => signIn()} 
                  size="sm" 
                  className="bg-[#00C7B0] hover:bg-[#00B3A0] text-white rounded-full px-6"
                >
                  {header.cta.login}
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Menu Button */}
        <div className="flex md:hidden">
          <ThemeToggle />
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="h-9 w-9">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <SheetHeader>
                <SheetTitle>{header.logo}</SheetTitle>
              </SheetHeader>
              
              {/* 移动端搜索框 */}
              <div className="relative mt-4 mb-2">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder={t('placeholder')}
                  className="pl-8 py-1 text-sm rounded-md w-full"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    if (e.target.value.trim().length >= 3) {
                      debouncedFetch(e.target.value);
                    }
                  }}
                  autoComplete="off"
                />
              </div>
              
              {/* 移动端搜索结果 */}
              {searchQuery.trim().length >= 3 && suggestions.length > 0 && (
                <div className="mt-2 border rounded-md divide-y">
                  {suggestions.map((suggestion) => (
                    <div 
                      key={suggestion.channelId}
                      className="flex items-center gap-2 p-2 hover:bg-muted cursor-pointer"
                      onClick={() => {
                        window.location.href = `/${currentLocale}/channel/${encodeURIComponent(suggestion.channelId)}`;
                      }}
                    >
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={suggestion.thumbnailUrl || "/images/placeholder.jpg"} />
                        <AvatarFallback>{suggestion.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-xs truncate">{suggestion.title}</p>
                        {suggestion.customUrl && (
                          <p className="text-xs text-muted-foreground truncate">
                            {suggestion.customUrl}
                          </p>
                        )}
                        <p className="text-xs text-muted-foreground truncate">
                          {formatSubscribers(suggestion.subscriberCount)} {t('subscribers')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              <div className="flex flex-col space-y-4 mt-6">
                {Object.entries(header.nav).map(([key, value]) => (
                  <Link
                    key={key}
                    href={key === 'pricing' ? '/pricing' : `#${key.toLowerCase()}`}
                    className="text-sm text-gray-600 hover:text-gray-900"
                  >
                    {value}
                  </Link>
                ))}
                <div className="flex items-center space-x-1 text-sm text-gray-600" onClick={() => switchLocale(currentLocale)}>
                  <Globe className="h-4 w-4" />
                  <span>{localeNames[currentLocale]}</span>
                </div>
                {session ? (
                  <div className="flex flex-col space-y-3 pt-4">
                    <div className="font-medium">{session.user?.name}</div>
                    <div className="text-gray-500">{session.user?.email}</div>
                    <Link
                      href={`/${currentLocale}/orders`}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-pink-50"
                    >
                      {header.userMenu.myOrders}
                    </Link>
                    <Link
                      href={`/${currentLocale}/profile`}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-pink-50"
                    >
                      {header.userMenu.profile}
                    </Link>
                    <button
                      type="button"
                      onClick={() => signOut({ callbackUrl: `/${currentLocale}` })}
                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-pink-50"
                    >
                      {header.userMenu.signOut}
                    </button>
                  </div>
                ) : (
                  <div className="flex flex-col space-y-3 pt-4">
                    <Button 
                      onClick={() => signIn()} 
                      size="sm" 
                      className="bg-[#00C7B0] hover:bg-[#00B3A0] text-white rounded-full"
                    >
                      {header.cta.login}
                    </Button>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </nav>
    </div>
  );
}