"use client"

import { useState, useEffect } from "react"
import <PERSON> from "next/link"
import { ArrowRight, Search, BookOpen } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Blog post data structure
interface BlogPost {
  id: string
  title: string
  excerpt: string
  content: string
  author: {
    name: string
    avatar: string
    bio: string
  }
  publishDate: string
  readTime: string
  category: string
  tags: string[]
  featuredImage: string
  slug: string
}

// Sample blog posts with substantial content about YouTube analytics
const blogPosts: BlogPost[] = [
  {
    id: "1",
    title: "The Complete Guide to YouTube Analytics: Understanding Your Channel's Performance",
    excerpt: "Master the art of YouTube analytics with our comprehensive guide. Learn how to interpret key metrics, identify growth opportunities, and optimize your content strategy for maximum engagement and revenue.",
    content: `YouTube analytics can seem overwhelming at first, but understanding your channel's performance data is crucial for growth and success. In this comprehensive guide, we'll break down everything you need to know about YouTube analytics, from basic metrics to advanced insights that can transform your content strategy.

## Understanding Core YouTube Metrics

### 1. Watch Time and Average View Duration
Watch time is arguably the most important metric on YouTube. It represents the total amount of time viewers spend watching your videos. YouTube's algorithm heavily favors videos with high watch time because it indicates engaging content that keeps viewers on the platform.

Average view duration shows how long, on average, viewers watch your videos. A higher average view duration suggests that your content is engaging and relevant to your audience. To improve this metric:

- Hook viewers in the first 15 seconds
- Create compelling thumbnails and titles
- Structure your content with clear segments
- Use pattern interrupts to maintain attention

### 2. Click-Through Rate (CTR)
Your click-through rate measures how often people click on your video after seeing the thumbnail and title. A good CTR varies by niche, but generally:
- 2-10% is considered good for most channels
- New channels often see lower CTRs initially
- CTR typically decreases as your video reaches broader audiences

### 3. Subscriber Growth and Retention
Monitor not just how many subscribers you gain, but also:
- Subscriber retention rates
- Where subscribers are coming from
- Which videos drive the most subscriptions
- Subscriber engagement levels

## Advanced Analytics Strategies

### Audience Retention Analysis
The audience retention graph shows you exactly when viewers drop off during your videos. Use this data to:
- Identify weak points in your content
- Understand which topics resonate most
- Optimize video pacing and structure
- Create more engaging introductions

### Traffic Source Analysis
Understanding where your views come from helps optimize your promotion strategy:
- YouTube Search: Focus on SEO optimization
- Suggested Videos: Create content similar to high-performing videos
- External Sources: Leverage social media and websites
- Direct Traffic: Build a loyal audience base

### Revenue Analytics
For monetized channels, revenue analytics provide insights into:
- RPM (Revenue Per Mille) trends
- Ad performance by video type
- Channel membership growth
- Super Chat and Super Thanks revenue

## Implementing Data-Driven Content Strategy

### Content Performance Analysis
Regularly analyze your top-performing content to identify patterns:
- Common topics or themes
- Optimal video lengths
- Best posting times
- Successful thumbnail styles

### Competitive Analysis
Use analytics to understand your competitive landscape:
- Compare growth rates with similar channels
- Analyze successful content in your niche
- Identify content gaps and opportunities
- Monitor trending topics and formats

### Seasonal Trends and Planning
YouTube analytics can help you identify seasonal patterns:
- Plan content around peak engagement periods
- Prepare for seasonal dips in viewership
- Capitalize on holiday and event-driven content
- Adjust posting schedules based on audience behavior

## Tools and Resources for Enhanced Analytics

### Third-Party Analytics Tools
While YouTube's native analytics are comprehensive, additional tools can provide deeper insights:
- VidIQ for keyword research and competitor analysis
- TubeBuddy for optimization and A/B testing
- Social Blade for historical data and projections
- Google Analytics for website traffic from YouTube

### Setting Up Custom Tracking
Implement advanced tracking for better insights:
- UTM parameters for external traffic
- Google Analytics goals for conversions
- Custom audiences for retargeting
- Conversion tracking for business objectives

## Common Analytics Mistakes to Avoid

### 1. Focusing Only on Vanity Metrics
Views and subscribers are important, but don't ignore:
- Engagement rates
- Watch time quality
- Conversion metrics
- Revenue per view

### 2. Not Acting on Data
Analytics are only valuable if you use them to make informed decisions:
- Set regular review schedules
- Create action plans based on insights
- Test and iterate based on data
- Document what works and what doesn't

### 3. Comparing Apples to Oranges
Ensure fair comparisons by considering:
- Video age and promotion levels
- Seasonal factors
- Algorithm changes
- Content type differences

## Future-Proofing Your Analytics Strategy

### Staying Updated with Platform Changes
YouTube regularly updates its analytics features and algorithm:
- Follow YouTube Creator Insider for updates
- Join creator communities for shared insights
- Attend YouTube events and workshops
- Experiment with new features early

### Building Long-Term Analytics Habits
Develop sustainable analytics practices:
- Create weekly and monthly review routines
- Set realistic goals and benchmarks
- Focus on trends rather than daily fluctuations
- Build a data-driven content calendar

## Conclusion

Mastering YouTube analytics is an ongoing process that requires consistent attention and analysis. By understanding your metrics, implementing data-driven strategies, and staying updated with platform changes, you can significantly improve your channel's performance and achieve your content goals.

Remember, analytics should inform your creative decisions, not replace your creative instincts. Use data as a guide to create better content that serves your audience while achieving your business objectives.`,
    author: {
      name: "Sarah Chen",
      avatar: "/images/authors/sarah-chen.jpg",
      bio: "YouTube Analytics Expert and Content Strategy Consultant with 8+ years of experience helping creators grow their channels."
    },
    publishDate: "2024-01-15",
    readTime: "12 min read",
    category: "Analytics",
    tags: ["YouTube Analytics", "Content Strategy", "Channel Growth", "Data Analysis"],
    featuredImage: "/images/blog/youtube-analytics-guide.jpg",
    slug: "complete-guide-youtube-analytics"
  },
  {
    id: "2",
    title: "YouTube SEO Mastery: How to Rank Your Videos Higher in Search Results",
    excerpt: "Discover the secrets of YouTube SEO and learn how to optimize your videos for maximum visibility. From keyword research to thumbnail optimization, master every aspect of YouTube search optimization.",
    content: `YouTube is the world's second-largest search engine, making SEO optimization crucial for video discovery and channel growth. This comprehensive guide will teach you everything you need to know about YouTube SEO, from basic optimization techniques to advanced strategies used by top creators.

## Understanding YouTube's Search Algorithm

### How YouTube Search Works
YouTube's search algorithm considers multiple factors when ranking videos:
- Relevance to the search query
- Video engagement metrics
- Video quality and freshness
- Channel authority and consistency
- User behavior and preferences

### The Importance of Keywords
Keywords are the foundation of YouTube SEO. They help YouTube understand what your video is about and when to show it to users. Effective keyword research involves:
- Understanding your audience's search behavior
- Identifying high-volume, low-competition keywords
- Using long-tail keywords for specific topics
- Analyzing competitor keyword strategies

## Comprehensive Keyword Research Strategy

### Tools for YouTube Keyword Research
1. **YouTube Search Suggest**: Start typing in YouTube's search bar to see autocomplete suggestions
2. **Google Keyword Planner**: Find search volumes and related keywords
3. **VidIQ**: Comprehensive YouTube SEO tool with keyword research features
4. **TubeBuddy**: Browser extension for YouTube optimization
5. **Ahrefs Keywords Explorer**: Advanced keyword research with YouTube-specific data

### Keyword Research Process
1. **Brainstorm seed keywords** related to your niche
2. **Expand your list** using keyword research tools
3. **Analyze search volume and competition** for each keyword
4. **Identify long-tail opportunities** with lower competition
5. **Group keywords by topic** for content planning

## Optimizing Video Elements for SEO

### Title Optimization
Your video title is one of the most important ranking factors:
- Include your primary keyword near the beginning
- Keep titles under 60 characters for full visibility
- Make titles compelling and click-worthy
- Use emotional triggers and power words
- Test different title variations

### Description Optimization
Video descriptions provide context and additional ranking opportunities:
- Write detailed descriptions (200+ words)
- Include primary and secondary keywords naturally
- Add timestamps for longer videos
- Include relevant links and calls-to-action
- Use the first 125 characters effectively (visible in search)

### Tags Strategy
While less important than before, tags still help with discoverability:
- Use 5-8 relevant tags per video
- Include your primary keyword as the first tag
- Mix broad and specific tags
- Use variations and synonyms of your main keywords
- Research competitor tags for inspiration

### Thumbnail Optimization
Thumbnails significantly impact click-through rates:
- Use high-contrast colors and clear imagery
- Include text overlays for context
- Maintain consistent branding across thumbnails
- Test different thumbnail styles
- Ensure thumbnails are readable on mobile devices

## Advanced YouTube SEO Techniques

### Video Content Optimization
The content itself plays a crucial role in SEO:
- Mention your target keywords in the video
- Create comprehensive content that fully covers the topic
- Encourage engagement through questions and calls-to-action
- Use closed captions and transcripts
- Structure content with clear sections and topics

### Playlist Optimization
Playlists can boost your SEO efforts:
- Create keyword-optimized playlist titles
- Write detailed playlist descriptions
- Organize videos logically within playlists
- Use playlists to target additional keywords
- Cross-promote playlists in video descriptions

### End Screens and Cards
These features can improve session duration:
- Promote related videos to keep viewers on your channel
- Use cards to highlight relevant content during the video
- Design end screens that encourage further viewing
- Link to playlists and subscribe buttons
- Analyze performance and optimize placement

## Technical SEO Considerations

### Video File Optimization
Optimize your video files before uploading:
- Use descriptive file names with keywords
- Choose appropriate video formats (MP4 recommended)
- Optimize video quality and file size
- Include metadata in video files
- Use consistent naming conventions

### Channel SEO
Your channel itself needs optimization:
- Create a keyword-rich channel description
- Use channel keywords in the about section
- Organize content into themed playlists
- Maintain consistent branding and messaging
- Optimize channel trailer for new visitors

### Closed Captions and Transcripts
These improve accessibility and SEO:
- Upload accurate closed captions
- Include keywords naturally in transcripts
- Use captions to improve user experience
- Consider multiple language captions for global reach
- Leverage auto-generated captions as a starting point

## Measuring and Improving SEO Performance

### Key SEO Metrics to Track
Monitor these metrics to gauge SEO success:
- Organic search traffic from YouTube
- Keyword rankings for target terms
- Click-through rates from search results
- Average view duration from search traffic
- Subscriber growth from search discovery

### SEO Analysis and Optimization
Regular analysis helps improve performance:
- Review YouTube Analytics search traffic data
- Identify top-performing keywords and content
- Analyze competitor SEO strategies
- Test and iterate on optimization techniques
- Document successful SEO practices

### Long-Term SEO Strategy
Build sustainable SEO practices:
- Create content clusters around main topics
- Develop topical authority in your niche
- Maintain consistent publishing schedules
- Build relationships with other creators
- Stay updated with YouTube algorithm changes

## Common YouTube SEO Mistakes

### Keyword Stuffing
Avoid overusing keywords:
- Focus on natural keyword integration
- Prioritize user experience over keyword density
- Use synonyms and related terms
- Write for humans, not just algorithms
- Balance optimization with readability

### Neglecting User Intent
Understand what users really want:
- Research the intent behind keywords
- Create content that satisfies user needs
- Analyze top-ranking videos for insights
- Focus on providing value over optimization
- Consider the user journey and next steps

### Ignoring Analytics
Data-driven optimization is essential:
- Regularly review SEO performance metrics
- Identify trends and patterns in successful content
- Adjust strategies based on performance data
- Test new optimization techniques
- Learn from both successes and failures

## Future of YouTube SEO

### Emerging Trends
Stay ahead of SEO evolution:
- Voice search optimization
- AI-powered content recommendations
- Mobile-first indexing and optimization
- Video chapters and structured data
- Integration with Google Search results

### Preparing for Algorithm Changes
Build resilient SEO strategies:
- Focus on creating high-quality, valuable content
- Diversify traffic sources beyond search
- Build strong audience relationships
- Stay informed about platform updates
- Adapt quickly to algorithm changes

## Conclusion

YouTube SEO is a powerful tool for growing your channel and reaching new audiences. By implementing these strategies consistently and staying updated with best practices, you can significantly improve your video rankings and channel visibility.

Remember that SEO is a long-term strategy that requires patience and persistence. Focus on creating valuable content for your audience while optimizing for search engines, and you'll see sustainable growth over time.`,
    author: {
      name: "Marcus Rodriguez",
      avatar: "/images/authors/marcus-rodriguez.jpg",
      bio: "SEO Specialist and YouTube Growth Expert with over 10 years of experience in digital marketing and content optimization."
    },
    publishDate: "2024-01-10",
    readTime: "15 min read",
    category: "SEO",
    tags: ["YouTube SEO", "Video Optimization", "Keyword Research", "Search Rankings"],
    featuredImage: "/images/blog/youtube-seo-mastery.jpg",
    slug: "youtube-seo-mastery-guide"
  }
]

export default function BlogPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [filteredPosts, setFilteredPosts] = useState(blogPosts)

  // Filter posts based on search and category
  const filterPosts = () => {
    let filtered = blogPosts

    if (selectedCategory !== "all") {
      filtered = filtered.filter(post => post.category.toLowerCase() === selectedCategory.toLowerCase())
    }

    if (searchQuery) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    setFilteredPosts(filtered)
  }

  // Update filters when search or category changes
  useEffect(() => {
    filterPosts()
  }, [searchQuery, selectedCategory])

  const categories = ["all", "analytics", "seo", "content strategy", "monetization", "growth"]

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">YouTube Analytics Blog</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Expert insights, strategies, and guides to help you master YouTube analytics, 
            grow your channel, and maximize your content's potential.
          </p>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row gap-4 mb-8 max-w-2xl mx-auto">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Featured Article */}
        {filteredPosts.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Featured Article</h2>
            <Card className="overflow-hidden">
              <div className="md:flex">
                <div className="md:w-1/2">
                  <div className="h-64 md:h-full bg-muted flex items-center justify-center">
                    <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/5 flex items-center justify-center">
                      <div className="text-center p-8">
                        <BookOpen className="h-16 w-16 mx-auto mb-4 text-primary/60" />
                        <p className="text-sm text-muted-foreground">Featured Article</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="md:w-1/2 p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="secondary">{filteredPosts[0].category}</Badge>
                    <span className="text-sm text-muted-foreground">•</span>
                    <span className="text-sm text-muted-foreground">{filteredPosts[0].readTime}</span>
                  </div>
                  <h3 className="text-2xl font-bold mb-3">{filteredPosts[0].title}</h3>
                  <p className="text-muted-foreground mb-4">{filteredPosts[0].excerpt}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={filteredPosts[0].author.avatar} />
                        <AvatarFallback>{filteredPosts[0].author.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{filteredPosts[0].author.name}</p>
                        <p className="text-xs text-muted-foreground">{filteredPosts[0].publishDate}</p>
                      </div>
                    </div>
                    <Button asChild>
                      <Link href={`/blog/${filteredPosts[0].slug}`}>
                        Read More <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Articles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPosts.slice(1).map((post) => (
            <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="h-48 bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center">
                <div className="text-center p-4">
                  <BookOpen className="h-12 w-12 mx-auto mb-2 text-primary/60" />
                  <p className="text-xs text-muted-foreground">{post.category}</p>
                </div>
              </div>
              <CardContent className="p-6">
                <div className="flex items-center gap-2 mb-3">
                  <Badge variant="outline">{post.category}</Badge>
                  <span className="text-sm text-muted-foreground">•</span>
                  <span className="text-sm text-muted-foreground">{post.readTime}</span>
                </div>
                <h3 className="text-lg font-semibold mb-2 line-clamp-2">{post.title}</h3>
                <p className="text-muted-foreground text-sm mb-4 line-clamp-3">{post.excerpt}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={post.author.avatar} />
                      <AvatarFallback>{post.author.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <span className="text-xs text-muted-foreground">{post.author.name}</span>
                  </div>
                  <Button variant="ghost" size="sm" asChild>
                    <Link href={`/blog/${post.slug}`}>
                      Read More
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredPosts.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-lg font-semibold mb-2">No articles found</h3>
            <p className="text-muted-foreground">Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </div>
    </div>
  )
}
