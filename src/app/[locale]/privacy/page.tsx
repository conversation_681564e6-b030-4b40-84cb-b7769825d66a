import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Privacy Policy - ReYoutube',
  description: 'Learn how ReYoutube protects your privacy and handles your personal information. Our commitment to data security and transparency.',
  keywords: 'privacy policy, data protection, GDPR compliance, user privacy, ReYoutube privacy',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    title: 'Privacy Policy - ReYoutube',
    description: 'Learn how ReYoutube protects your privacy and handles your personal information. Our commitment to data security and transparency.',
    type: 'website',
  },
  twitter: {
    card: 'summary',
    title: 'Privacy Policy - ReYoutube',
    description: 'Learn how ReYoutube protects your privacy and handles your personal information.',
  },
}
import { Badge } from "@/components/ui/badge"
import { Shield, Eye, Lock, Database, Cookie, Mail } from "lucide-react"

"use client"

export default function PrivacyPolicyPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge variant="outline" className="mb-4">Legal</Badge>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Privacy Policy</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Your privacy is important to us. This policy explains how ReYoutube collects, 
            uses, and protects your personal information.
          </p>
          <p className="text-sm text-muted-foreground mt-4">
            Last updated: January 15, 2024
          </p>
        </div>

        {/* Quick Overview */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Privacy at a Glance</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6 text-center">
                <Shield className="h-12 w-12 mx-auto mb-4 text-green-500" />
                <h3 className="font-semibold mb-2">Data Protection</h3>
                <p className="text-sm text-muted-foreground">
                  We use enterprise-grade security to protect your data
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <Eye className="h-12 w-12 mx-auto mb-4 text-blue-500" />
                <h3 className="font-semibold mb-2">Transparency</h3>
                <p className="text-sm text-muted-foreground">
                  Clear information about what data we collect and why
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <Lock className="h-12 w-12 mx-auto mb-4 text-purple-500" />
                <h3 className="font-semibold mb-2">Your Control</h3>
                <p className="text-sm text-muted-foreground">
                  You control your data and can request deletion anytime
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto space-y-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Information We Collect
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Account Information</h4>
                <p className="text-muted-foreground">
                  When you create an account, we collect your email address, name, and profile information. 
                  If you sign up using Google OAuth, we receive basic profile information from Google.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Usage Data</h4>
                <p className="text-muted-foreground">
                  We collect information about how you use our service, including pages visited, 
                  features used, search queries, and interaction patterns to improve our platform.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">YouTube Channel Data</h4>
                <p className="text-muted-foreground">
                  When you analyze YouTube channels, we collect publicly available data from YouTube's API, 
                  including channel statistics, video metadata, and performance metrics.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Technical Information</h4>
                <p className="text-muted-foreground">
                  We automatically collect technical information such as IP address, browser type, 
                  device information, and operating system for security and optimization purposes.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>How We Use Your Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <ul className="space-y-3">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div>
                    <strong>Service Provision:</strong> To provide, maintain, and improve our YouTube analytics services
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div>
                    <strong>Communication:</strong> To send you service updates, security alerts, and support messages
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div>
                    <strong>Personalization:</strong> To customize your experience and provide relevant insights
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div>
                    <strong>Analytics:</strong> To understand usage patterns and improve our platform
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div>
                    <strong>Security:</strong> To protect against fraud, abuse, and security threats
                  </div>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Data Sharing and Disclosure</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                We do not sell, trade, or rent your personal information to third parties. We may share your information only in the following circumstances:
              </p>
              <ul className="space-y-3">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div>
                    <strong>Service Providers:</strong> With trusted third-party services that help us operate our platform (hosting, analytics, payment processing)
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div>
                    <strong>Legal Requirements:</strong> When required by law or to protect our rights and safety
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div>
                    <strong>Business Transfers:</strong> In connection with a merger, acquisition, or sale of assets
                  </div>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Cookie className="h-5 w-5" />
                Cookies and Tracking
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                We use cookies and similar technologies to enhance your experience:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Essential Cookies</h4>
                  <p className="text-sm text-muted-foreground">
                    Required for basic site functionality, authentication, and security
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Analytics Cookies</h4>
                  <p className="text-sm text-muted-foreground">
                    Help us understand how users interact with our platform
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Preference Cookies</h4>
                  <p className="text-sm text-muted-foreground">
                    Remember your settings and preferences for a better experience
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Marketing Cookies</h4>
                  <p className="text-sm text-muted-foreground">
                    Used to deliver relevant advertisements and measure campaign effectiveness
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Your Rights and Choices</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                You have the following rights regarding your personal information:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div>
                    <h4 className="font-semibold">Access</h4>
                    <p className="text-sm text-muted-foreground">Request a copy of your personal data</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Correction</h4>
                    <p className="text-sm text-muted-foreground">Update or correct inaccurate information</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Deletion</h4>
                    <p className="text-sm text-muted-foreground">Request deletion of your personal data</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-semibold">Portability</h4>
                    <p className="text-sm text-muted-foreground">Export your data in a machine-readable format</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Objection</h4>
                    <p className="text-sm text-muted-foreground">Object to certain processing activities</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Restriction</h4>
                    <p className="text-sm text-muted-foreground">Limit how we process your information</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Data Security</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                We implement industry-standard security measures to protect your information:
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm">End-to-end encryption for data transmission</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Secure data storage with encryption at rest</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Regular security audits and vulnerability assessments</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Access controls and authentication mechanisms</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm">SOC 2 Type II compliance</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Contact Us
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                If you have questions about this Privacy Policy or want to exercise your rights, please contact us:
              </p>
              <div className="space-y-2">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Address:</strong> ReYoutube Inc., 123 Analytics Street, Data City, DC 12345</p>
                <p><strong>Data Protection Officer:</strong> <EMAIL></p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Changes to This Policy</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                We may update this Privacy Policy from time to time. We will notify you of any material changes 
                by posting the new policy on this page and updating the "Last updated" date. We encourage you to 
                review this policy periodically to stay informed about how we protect your information.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
