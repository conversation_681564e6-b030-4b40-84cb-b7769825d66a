"use client"

import Image from "next/image"
import Link from "next/link"
import { Bar<PERSON>hart3, Users, TrendingUp, Award, Globe, Shield, Zap, Heart } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function AboutPage() {
  const stats = [
    { label: "Active Users", value: "50,000+", icon: Users },
    { label: "Channels Analyzed", value: "1M+", icon: BarChart3 },
    { label: "Data Points", value: "10B+", icon: TrendingUp },
    { label: "Countries", value: "150+", icon: Globe }
  ]

  const values = [
    {
      icon: Shield,
      title: "Data Privacy",
      description: "We prioritize user privacy and data security with enterprise-grade protection and transparent practices."
    },
    {
      icon: Zap,
      title: "Innovation",
      description: "Continuously pushing the boundaries of YouTube analytics with cutting-edge AI and machine learning technologies."
    },
    {
      icon: Heart,
      title: "Creator Success",
      description: "Dedicated to empowering creators with the tools and insights they need to build successful YouTube channels."
    },
    {
      icon: Award,
      title: "Excellence",
      description: "Committed to delivering the highest quality analytics platform with accurate data and reliable insights."
    }
  ]

  const team = [
    {
      name: "<PERSON>",
      role: "CEO & Co-founder",
      bio: "Former YouTube Product Manager with 10+ years in digital analytics and creator economy.",
      image: "/images/team/sarah-chen.jpg"
    },
    {
      name: "Marcus Rodriguez",
      role: "CTO & Co-founder",
      bio: "Ex-Google engineer specializing in large-scale data processing and machine learning systems.",
      image: "/images/team/marcus-rodriguez.jpg"
    },
    {
      name: "Emily Watson",
      role: "Head of Analytics",
      bio: "Data scientist with expertise in social media analytics and predictive modeling.",
      image: "/images/team/emily-watson.jpg"
    },
    {
      name: "David Kim",
      role: "Head of Product",
      bio: "Product leader focused on creator tools and user experience optimization.",
      image: "/images/team/david-kim.jpg"
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">About ReYoutube</Badge>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Empowering YouTube Creators with Data-Driven Insights
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            ReYoutube is the world's most comprehensive YouTube analytics platform, trusted by over 50,000 creators, 
            marketers, and analysts to grow their channels and maximize their impact on the platform.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/signup">Get Started Free</Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/demo">Watch Demo</Link>
            </Button>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat) => (
            <Card key={stat.label}>
              <CardContent className="p-6 text-center">
                <stat.icon className="h-8 w-8 mx-auto mb-3 text-primary" />
                <div className="text-2xl font-bold mb-1">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Mission Section */}
        <div className="mb-16">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Our Mission</h2>
            <p className="text-lg text-muted-foreground mb-8">
              We believe that every creator deserves access to professional-grade analytics and insights. 
              Our mission is to democratize YouTube analytics, making advanced data analysis accessible 
              to creators of all sizes, from individual content creators to large media organizations.
            </p>
            <div className="bg-gradient-to-r from-primary/10 to-primary/5 p-8 rounded-lg border border-primary/20">
              <h3 className="text-xl font-semibold mb-4">Why We Started ReYoutube</h3>
              <p className="text-muted-foreground">
                Founded in 2022 by former YouTube and Google employees, ReYoutube was born from the frustration 
                of seeing talented creators struggle with limited analytics tools. We recognized that while YouTube 
                provided basic metrics, creators needed deeper insights to truly understand their audience and 
                optimize their content strategy. Today, we're proud to be the leading YouTube analytics platform, 
                helping creators worldwide achieve their goals.
              </p>
            </div>
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Our Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value) => (
              <Card key={value.title}>
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-primary/10 text-primary p-3 rounded-full">
                      <value.icon className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">{value.title}</h3>
                      <p className="text-muted-foreground">{value.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Meet Our Team</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member) => (
              <Card key={member.name}>
                <CardContent className="p-6 text-center">
                  <div className="w-24 h-24 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
                    <Image
                      src={member.image}
                      alt={member.name}
                      width={96}
                      height={96}
                      className="rounded-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = "/images/placeholder-avatar.jpg"
                      }}
                    />
                  </div>
                  <h3 className="font-semibold mb-1">{member.name}</h3>
                  <p className="text-sm text-primary mb-2">{member.role}</p>
                  <p className="text-xs text-muted-foreground">{member.bio}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Technology Section */}
        <div className="mb-16">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">Our Technology</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold mb-4">Advanced Analytics Engine</h3>
                <p className="text-muted-foreground mb-4">
                  Our proprietary analytics engine processes over 10 billion data points daily, providing 
                  real-time insights and predictive analytics that help creators stay ahead of trends.
                </p>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    Real-time data processing and updates
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    Machine learning-powered predictions
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    Advanced trend detection algorithms
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    Comprehensive competitive analysis
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-4">Enterprise-Grade Security</h3>
                <p className="text-muted-foreground mb-4">
                  We take data security seriously, implementing industry-leading security measures to 
                  protect user data and ensure privacy compliance across all jurisdictions.
                </p>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    End-to-end encryption for all data
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    GDPR and CCPA compliance
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    SOC 2 Type II certified infrastructure
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    Regular security audits and updates
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Awards and Recognition */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Awards & Recognition</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6 text-center">
                <Award className="h-12 w-12 mx-auto mb-4 text-yellow-500" />
                <h3 className="font-semibold mb-2">Best Analytics Tool 2024</h3>
                <p className="text-sm text-muted-foreground">Creator Economy Awards</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <Award className="h-12 w-12 mx-auto mb-4 text-blue-500" />
                <h3 className="font-semibold mb-2">Innovation in Data Science</h3>
                <p className="text-sm text-muted-foreground">Tech Innovation Summit 2024</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <Award className="h-12 w-12 mx-auto mb-4 text-green-500" />
                <h3 className="font-semibold mb-2">Top Startup 2023</h3>
                <p className="text-sm text-muted-foreground">Digital Media Awards</p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Ready to Grow Your Channel?</h2>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Join thousands of successful creators who trust ReYoutube to analyze their performance, 
                understand their audience, and optimize their content strategy.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <Link href="/signup">Start Free Trial</Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/contact">Contact Sales</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
